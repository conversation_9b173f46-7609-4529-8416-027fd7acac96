// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROJECTION_PROTOCOL_USER_DEFINED_SERVICE_H
#define AUTOLINK_PROJECTION_PROTOCOL_USER_DEFINED_SERVICE_H

#include "util/common.h"
#include "IUserDefinedCallbacks.h"
#include "ProtocolEndpointBase.h"

#include <string>

class UserDefined : public ProtocolEndpointBase {
public:
    UserDefined(uint8_t id, MessageRouter *router)
            : ProtocolEndpointBase(id, router, false) {}

    ~UserDefined() override = default;

    void onChannelOpened(uint8_t channelId, uint32_t extraMessage) override;

    void registerCallbacks(const shared_ptr<IUserDefinedCallbacks> &callbacks);

    void sendRawDataResponse();

    void sendRawDataRequest(const shared_ptr<IoBuffer> &msg);

    bool discoverService(const Service &srv) override;

    int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) override;

private:
    shared_ptr<IUserDefinedCallbacks> mCallbacks;

    int handleRawDataRequest(void *msg, size_t len);

    int handleRawDataResponse();

};

#endif
