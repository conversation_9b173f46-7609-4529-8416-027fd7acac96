// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "UserDefined.h"

void UserDefined::registerCallbacks(
        const shared_ptr<IUserDefinedCallbacks> &callbacks) {
    mCallbacks = callbacks;
}

void UserDefined::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    ProtocolEndpointBase::onChannelOpened(channelId, extraMessage);
    mCallbacks->onChannelOpened();
}

bool UserDefined::discoverService(const Service &srv) {
    bool ret = false;
    if (srv.has_user_defined_service()) {
        ret = true;
    }
    return ret;
}


int UserDefined::routeMessage(
        uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);

    switch (type) {
        case MSG_WLAN_USERDEF_RAWDATA_REQUEST: {
            ret = handleRawDataRequest(ptr,len);
            break;
        }
        case MSG_WLAN_USERDEF_RAWDATA_RESPONSE: {
            ret = handleRawDataResponse();
            break;
        }
        default:
            break;
    }
    return ret;
}

int UserDefined::handleRawDataRequest(void *msg, size_t len) {
    shared_ptr<IoBuffer> buf(new IoBuffer(len));
    memcpy(buf->raw(), msg, len);
    mCallbacks->onRawDataRequest(buf);
    return STATUS_SUCCESS;
}

int UserDefined::handleRawDataResponse() {
    mCallbacks->onRawDataResponse();
    return STATUS_SUCCESS;
}
