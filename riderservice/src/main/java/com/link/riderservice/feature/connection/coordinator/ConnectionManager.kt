package com.link.riderservice.feature.connection.coordinator

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.media.projection.MediaProjection
import android.net.ConnectivityManager
import android.net.LinkProperties
import android.net.NetworkCapabilities
import android.net.wifi.ScanResult
import android.view.Display
import com.link.riderservice.api.BleStatus
import com.link.riderservice.api.Connection
import com.link.riderservice.api.SPNaviDayOrNight
import com.link.riderservice.api.SPNaviMode
import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.api.SPWifiInfo
import com.link.riderservice.api.SocketStatus
import com.link.riderservice.api.WifiStatus
import com.link.riderservice.api.callback.BleListCallback
import com.link.riderservice.api.callback.BleStateCallback
import com.link.riderservice.api.callback.ChangeNaviModeCallback
import com.link.riderservice.api.callback.ConnectionStatusCallback
import com.link.riderservice.api.callback.NaviDayOrNightChangeCallback
import com.link.riderservice.api.callback.PermissionDetectionCallback
import com.link.riderservice.api.callback.PresentationCallback
import com.link.riderservice.api.callback.ReceiveCommonDataCallback
import com.link.riderservice.api.callback.SPRiderServicesExceptionNotiCallback
import com.link.riderservice.api.callback.WifiInfoCallback
import com.link.riderservice.api.dto.DeviceConfig
import com.link.riderservice.api.exception.AuthException
import com.link.riderservice.api.exception.BluetoothException
import com.link.riderservice.api.exception.DeviceException
import com.link.riderservice.api.exception.NetworkException
import com.link.riderservice.api.exception.ProtocolException
import com.link.riderservice.api.exception.WifiException
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.extensions.flow.collectWithScope
import com.link.riderservice.core.extensions.flow.setState
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.ble.QRCodeUtils
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logI
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.core.utils.system.AppBackgroundManager
import com.link.riderservice.core.utils.system.Platform
import com.link.riderservice.core.utils.system.ScreenBrightnessUtils
import com.link.riderservice.data.authorization.data.repository.AuthorizeRepositoryImpl
import com.link.riderservice.data.authorization.data.source.remote.AuthorizeRemoteSourceImpl
import com.link.riderservice.data.source.local.ConfigPreferences
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.analytics.SimpleConnectionLogger
import com.link.riderservice.feature.connection.ble.BleDevice
import com.link.riderservice.feature.connection.ble.RiderBleCallback
import com.link.riderservice.feature.connection.ble.RiderBleManager
import com.link.riderservice.feature.connection.mode.AutoLinkConnect
import com.link.riderservice.feature.connection.mode.WifiConnectionMode
import com.link.riderservice.feature.connection.recovery.ConnectionRecoveryManager
import com.link.riderservice.feature.connection.transport.tcp.TcpServerConnection.Companion.LISTEN_PORT
import com.link.riderservice.feature.connection.wifi.SoftIP2pListener
import com.link.riderservice.feature.connection.wifi.SoftP2pManagerNew
import com.link.riderservice.feature.connection.wifi.WiFiClientManager
import com.link.riderservice.feature.connection.wifi.WiFiClientManagerListener
import com.link.riderservice.feature.display.callback.DisplayNaviCallback
import com.link.riderservice.feature.display.manager.DisplayNaviManager
import com.link.riderservice.feature.messaging.MessageCallback
import com.link.riderservice.feature.messaging.MessageManager
import com.link.riderservice.feature.messaging.toByteArray
import com.link.riderservice.libs.ble.data.Data
import com.link.riderservice.protobuf.RiderProtocol
import com.link.riderservice.protobuf.RiderProtocol.NaviDayOrNight
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.retry
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.net.Inet4Address
import java.util.Timer
import java.util.TimerTask

internal object ConnectionManager {
    private const val TAG = "ConnectionManager"
    private const val NETWORK_ERROR = -1
    private const val SUCCESS = 0
    private val mutableConnectionStatus = MutableStateFlow(Connection())
    private val connectionStatus = mutableConnectionStatus.asStateFlow()

    private val bleListCallbacks: MutableList<WeakReference<BleListCallback>> = mutableListOf()
    private val bleStateCallbacks: MutableList<WeakReference<BleStateCallback>> = mutableListOf()
    private val wifiInfoCallbacks: MutableList<WeakReference<WifiInfoCallback>> = mutableListOf()
    private val changeNaviModeCallbacks: MutableList<WeakReference<ChangeNaviModeCallback>> =
        mutableListOf()
    private val connectionStatusCallbacks: MutableList<WeakReference<ConnectionStatusCallback>> =
        mutableListOf()
    private val permissionDetectionCallbacks: MutableList<WeakReference<PermissionDetectionCallback>> =
        mutableListOf()
    private val riderServicesExceptionNotiCallbacks: MutableList<WeakReference<SPRiderServicesExceptionNotiCallback>> =
        mutableListOf()
    private val receiveCommonDataCallbacks: MutableList<WeakReference<ReceiveCommonDataCallback>> =
        mutableListOf()
    private val presentationCallbacks: MutableList<WeakReference<PresentationCallback>> =
        mutableListOf()
    private val naviDayOrNightChangeCallbacks: MutableList<WeakReference<NaviDayOrNightChangeCallback>> =
        mutableListOf()

    private var application: Application? = null
    private val authorizeRepository = AuthorizeRepositoryImpl(AuthorizeRemoteSourceImpl())

    private var isManualDisconnect = false
    private var connectionTimer: Timer? = null
    private var countDown: kotlinx.coroutines.Job? = null
    private var isAutolinkServiceStarted = false

    private val p2pManager by lazy { SoftP2pManagerNew(p2pListener) }
    private val wifiClientManager by lazy { WiFiClientManager(wifiClientListener) }
    private var displayNaviManager: DisplayNaviManager = DisplayNaviManager(
        onTcpConnecting = {
            ConnectionAnalytics.startSocketConnect()
        }
    )
    private var wifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT
    private var deviceConfig: DeviceConfig? = null
    private var productKey = "请在连接平台后查看"
    private var uuid = "请在连接平台后查看"
    private var sdkVersion = "请在连接平台后查看"
    private var protocolVersion = "请在连接平台后查看"
    private val connectionRecoveryManager = ConnectionRecoveryManager()
    private var spWifiInfo = SPWifiInfo(WifiConnectionMode.WIFI_AP_CLIENT, "", "")
    private var naviMode: SPNaviMode = SPNaviMode.SPNaviModeSimpleNavi

    // Navigation state management
    private var currentNaviMode: SPNaviMode = SPNaviMode.SPNaviModeNoNavi
    private var previousNaviMode: SPNaviMode = SPNaviMode.SPNaviModeNoNavi

    private val messageCallback = object : MessageCallback {
        override fun onMessage(type: Int, msg: ByteArray?) {
            when (type) {
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_NOTIFICATION_VALUE -> {
                    handleWifiInfo(msg)
                }

                RiderProtocol.NaviMessageId.MSG_AUTOLINK_STATE_NOTIFICATION_VALUE -> {
                    handleAutolinkState(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_RESPONSE_VALUE -> {
                    handleNaviResponse(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_CHANGE_VALUE -> {
                    val naviModeChange = RiderProtocol.NaviModeChange.parseFrom(msg)
                    sendNaviModeChange(convertProtocolNaviMode(naviModeChange.naviMode))
                }

                RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_REQUEST_VALUE -> {
                    requestWeatherInfo()
                }

                RiderProtocol.NaviMessageId.MSG_CONFIG_NOTIFICATION_VALUE -> {
                    handleConfig(msg)
                }

                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_NOTIFICATION_VALUE -> {
                    handleProtocolVersion(msg)
                }

                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_REQUEST_VALUE -> {
                    handleCompliance(msg)
                }

                RiderProtocol.NaviMessageId.MSG_ACTIVE_REQUEST_VALUE -> {
                    handleActiveRequest(msg)
                }

                RiderProtocol.NaviMessageId.MSG_AUTHORIZATION_NOTIFICATION_VALUE -> {
                    handleAuthorization(msg)
                }

                RiderProtocol.NaviMessageId.MSG_TIME_REQUEST_VALUE -> {
                    sendTimeInfo()
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_START_RESPONSE_VALUE -> {
                    handleNaviModeStart(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_STOP_RESPONSE_VALUE -> {
                    handleNaviModeStop(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_CLOSE_CONNECTION_VALUE -> {
                    closeConnect()
                }


                RiderProtocol.NaviMessageId.MSG_NAVI_DAY_OR_NIGHT_VALUE -> {
                    handleNaviDayOrNight(msg)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_VERSION_RESPONSE_VALUE -> {
                    sdkVersion = RiderProtocol.NaviVersionResponse.parseFrom(msg).naviVersion
                }

                RiderProtocol.NaviMessageId.MSG_BT_USERDEF_RAWDATA_REQUEST_VALUE -> {
                    handleUserDefRawDataRequest(msg)
                }

                else -> {
                    logW(TAG, "unknown message type: $type")
                }
            }
        }
    }

    private fun handleUserDefRawDataRequest(msg: ByteArray?) {
        msg?.let { value ->
            receiveCommonDataCallbacks.forEach {
                it.get()?.onReceiveCommonData(value)
            }
        }
    }


    private fun convertProtocolNaviMode(naviMode: RiderProtocol.NaviMode): SPNaviMode =
        when (naviMode) {
            RiderProtocol.NaviMode.NO_NAVI -> SPNaviMode.SPNaviModeNoNavi
            RiderProtocol.NaviMode.SIMPLE_NAVI -> SPNaviMode.SPNaviModeSimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> SPNaviMode.SPNaviModeScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> SPNaviMode.SPNaviModeMirrorNavi
            RiderProtocol.NaviMode.CRUISE_NAVI -> SPNaviMode.SPNaviModeCruiseNavi
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> SPNaviMode.SPNaviModeLockScreenNavi
            else -> SPNaviMode.SPNaviModeDefaultNavi
        }


    private fun handleWifiInfo(message: ByteArray?) {
        val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(message)
        logD(
            TAG,
            "Handling WiFi info for mode: $wifiConnectionMode. Received SSID: ${wifiInfo.name}, Password: ${wifiInfo.password}"
        )
        spWifiInfo = SPWifiInfo(wifiConnectionMode, wifiInfo.name, wifiInfo.password)

        when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logD(TAG, "Using AP connection mode")
                val instrumentSsid = wifiInfo.name
                val instrumentPassword = wifiInfo.password

                if (instrumentSsid.isNotEmpty()) {
                    setPotentialApCredentials(instrumentSsid, instrumentPassword)
                    application?.let {
                        connectToInstrumentWifi(
                            it.applicationContext,
                            instrumentSsid,
                            instrumentPassword
                        )
                    }
                } else {
                    logE(TAG, "AP Mode: Invalid AP WiFi info received (SSID is empty)")
                }
            }

            WifiConnectionMode.WIFI_P2P -> {
                logD(TAG, "Using P2P connection mode")
                application?.let {
                    startSearchWifiAndConnect(
                        it.applicationContext,
                        wifiInfo.address,
                        wifiInfo.port
                    )
                }
            }
        }
    }

    private fun handleConfig(message: ByteArray?) {
        val config = RiderProtocol.ConfigNotification.parseFrom(message)
        deviceConfig = DeviceConfig(
            isSupportDvr = config.isSupportDvr,
            isSupportNavi = config.isSupportNavi,
            isSupportScreenNavi = config.isSupportScreenNavi,
            isSupportWeather = config.isSupportWeaNoti,
            isSupportNotification = config.isSupportAndroidNoti,
            isSupportCircularScreen = config.isSupportCircularScreen,
            isSupportCruise = config.isSupportCruiseNavi,
            isSupportMirror = config.isSupportMirrorScreen
        )
    }

    private fun handleAutolinkState(message: ByteArray?) {
        val stateNotification = RiderProtocol.AutoLinkStateNotification.parseFrom(message)
        logD(TAG, "autolink state:${stateNotification.state}")
        if (stateNotification.state == 0) {
            startAutolinkServiceSuccess()
        } else {
            disconnectWifi()
        }
    }

    private fun handleNaviResponse(message: ByteArray?) {
        val response = RiderProtocol.NaviResponse.parseFrom(message)
        sendNaviModeChangeResponse(
            response.naviMode,
            response.isReady
        )
    }

    private fun handleProtocolVersion(message: ByteArray?, isManualConnected: Boolean = false) {
        val version = RiderProtocol.ProtocolVerNotification.parseFrom(message)
        protocolVersion = "${version.major}.${version.minor}"
        checkVersion(version, isManualConnected)
    }

    private fun handleCompliance(message: ByteArray?) {
        val complianceRequest = RiderProtocol.ComplianceRequest.parseFrom(message)
        requestVerify(
            complianceRequest.productkey,
            complianceRequest.macAddr,
            complianceRequest.uuid,
            complianceRequest.time,
            complianceRequest.licenseSign,
            complianceRequest.sign
        )
        productKey = complianceRequest.productkey
        uuid = complianceRequest.uuid
    }

    private fun handleActiveRequest(message: ByteArray?) {
        val activeRequest = RiderProtocol.ActivateRequest.parseFrom(message)
        requestActive(activeRequest)
    }

    private fun requestActive(activeRequest: RiderProtocol.ActivateRequest) =
        requestActivate(
            activeRequest.productKey,
            activeRequest.macAddr,
            activeRequest.time,
            activeRequest.sign
        )

    private fun handleAuthorization(message: ByteArray?) {
        val authorizationResult = RiderProtocol.AuthorizationResult.parseFrom(message)
        if (!authorizationResult.result) {
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()
                    ?.onSPRiderServicesExceptionNoti(AuthException.AuthorizationFailed("设备授权验证失败"))
            }
            disconnect()
        }
    }

    private fun sendTimeInfo() {
        if (isBleConnected()) {
            MessageManager.sendTimeInfo()
        }
    }

    private fun handleNaviModeStart(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStartResponse.parseFrom(message)
        sendNaviModeStartResponse(response.naviMode)
    }

    private fun handleNaviModeStop(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStopResponse.parseFrom(message)
        sendNaviModeStopResponse(response.naviMode)
    }

    private fun handleNaviDayOrNight(message: ByteArray?) {
        val mapType = RiderProtocol.NaviModeDayOrNight.parseFrom(message)
        logD(TAG, "ChangeMap:${mapType.naviDayOrNight}")
        val naviTheme = when (mapType.naviDayOrNight) {
            NaviDayOrNight.NAVI_DAYTIME -> SPNaviDayOrNight.DAY
            NaviDayOrNight.NAVI_NIGHT -> SPNaviDayOrNight.NIGHT
            NaviDayOrNight.NAVI_AUTO -> SPNaviDayOrNight.AUTO
        }
        naviDayOrNightChangeCallbacks.forEach {
            it.get()?.onNviDayOrNightChange(naviTheme)
        }
    }

    // =================================================================================
    // BLE Management
    // =================================================================================
    private var bleAddress: String? = null
    private var bleName: String? = null

    private val bleCallback = object : RiderBleCallback {
        override fun onDataReceived(device: BluetoothDevice, receivedData: Data) {
            receivedData.value?.let { buffer ->
                MessageManager.enqueueIncoming(buffer, buffer.size)
            }
        }

        override fun onDeviceConnecting(device: BluetoothDevice) {
            logD(TAG, "onDeviceConnecting: $device ")
            mutableConnectionStatus.setState { copy(btStatus = BleStatus.DeviceConnecting(device)) }
        }

        @SuppressLint("MissingPermission")
        override fun onDeviceConnected(device: BluetoothDevice) {
            logD(TAG, "onDeviceConnected")
            bleAddress = device.address
            bleName = device.name
            mutableConnectionStatus.setState { copy(btStatus = BleStatus.DeviceConnected(device)) }
        }

        override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceFailedToConnect: $reason")
            ConnectionAnalytics.finishConnection(false, "BLE connection failed: reason $reason")

            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceFailedToConnect(device, reason))
            }
        }

        override fun onDeviceReady(device: BluetoothDevice) {
            logD(TAG, "onDeviceReady")
            loopWrite()
            isManualDisconnect = false
        }

        override fun onDeviceDisconnecting(device: BluetoothDevice) {
        }

        override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceDisconnected: $reason")
            cancelConnectWithTimeout()
            MessageManager.clearMessage()
            RiderBleManager.release(isManualDisconnect)
            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceDisconnected(device, reason))
            }
            disconnectWifiConnection()
            if (!isManualDisconnect) {
                startScan()
            }
        }

        override fun onScanResult(devices: List<BleDevice>) {
            bleListCallbacks.forEach {
                it.get()?.onBleListResult(devices)
            }
        }

        override fun onRequestBt() {
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()?.onSPRiderServicesExceptionNoti(BluetoothException.BluetoothDisabled())
            }
        }

        override fun onScanning() {
            bleStateCallbacks.forEach {
                it.get()?.onScanning()
            }
        }

        override fun onScanFinish() {
            bleStateCallbacks.forEach {
                it.get()?.onScanFinish()
            }
        }

        override fun onNeedBluetoothScanPermission() {
            permissionDetectionCallbacks.forEach {
                it.get()?.onNeedBluetoothScanPermission()
            }
        }

        override fun onNeedLocationPermission() {
            permissionDetectionCallbacks.forEach {
                it.get()?.onNeedLocationPermission()
            }
        }

        override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
            ConnectionAnalytics.finishConnection(false, "BLE notification failed: status $status")
            riderServicesExceptionNotiCallbacks.forEach {
                it.get()
                    ?.onSPRiderServicesExceptionNoti(DeviceException.FeatureNotSupported("BLE通知功能"))
            }
            disconnect()
        }
    }

    /**
     * 连接 BLE 设备
     */
    internal fun connectBle(device: BleDevice, context: Context) {
        RiderBleManager.connect(device.device, context)
    }

    /**
     * 断开连接
     */
    @Synchronized
    internal fun disconnect(isManual: Boolean = true) {
        isManualDisconnect = isManual
        MessageManager.clearMessage()
        RiderBleManager.release()
        displayNaviManager.sendByeByeRequest()
        displayNaviManager.release()
    }

    internal fun closeConnect() {
        MessageManager.clearMessage()
        RiderBleManager.closeConnect()
    }

    /**
     * 开启设备扫描
     */
    internal fun startScan(shouldAutoConnect: Boolean = true) {
        ConnectionAnalytics.startConnection("FULL_CONNECTION")

        RiderBleManager.startScan(shouldAutoConnect)
    }

    /**
     * 停止设置扫描
     */
    internal fun stopScan() {
        RiderBleManager.stopScan()
    }

    internal fun getBleList(): List<BleDevice> {
        return RiderBleManager.getBleList()
    }

    /**
     * BLE 设备是否连接
     * @return 是否连接
     */
    internal fun isBleConnected(): Boolean {
        return RiderBleManager.isConnected()
    }

    private fun loopWrite() {
        mainScope.launch(Dispatchers.IO) {
            while (RiderBleManager.isConnected()) {
                MessageManager.dequeueOutgoing()?.let {
                    RiderBleManager.write(it.toByteArray())
                }
            }
            RiderBleManager.release(false)
        }
    }

    // =================================================================================
    // Wi-Fi P2P Management
    // =================================================================================
    private var isP2pConnected = false

    private val p2pListener = object : SoftIP2pListener {
        override fun requestWifiInfo() {
            <EMAIL>()
        }

        override fun onWifiState(opened: Boolean) {
            if (!opened && connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
                riderServicesExceptionNotiCallbacks.forEach {
                    it.get()?.onSPRiderServicesExceptionNoti(WifiException.WifiDisabled())
                }
            }
        }

        override fun onCancelConnect() {
            logD(TAG, "P2P onCancelConnect")
            ConnectionAnalytics.finishConnection(false, "P2P connection cancelled or failed")

            if (isP2pConnected) {
                shutdown()
            } else {
                requestWifiInfo()
            }
        }

        override fun onWifiConnectSuccess() {
            handleP2pConnectionExist("P2P already connected")
        }

        override fun connectExist() {
            handleP2pConnectionExist("P2P connection exist")
        }

        override fun onP2pConnectSuccess() {
            logD(TAG, "P2P connect success")
            startConnectionEstablishing()
        }

        override fun onWifiDisconnect() {
            logD(TAG, "P2P onWifiDisconnect")
            if (isP2pConnected) {
                shutdown()
            }
            isP2pConnected = false
        }
    }

    /**
     * 搜索并连接 WLAN 直连 (P2P模式)
     * @param address 平台 MAC 地址
     * @param port 端口 默认 30512
     */
    private fun startSearchWifiAndConnect(context: Context, address: String, port: Int) {
        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            logD(TAG, "wifi already connected")
            return
        }
        ConnectionAnalytics.startWifiScan()
        val configPrefs =
            ConfigPreferences.getInstance(SPRiderServices.getSharedInstance().application)
        configPrefs.setBleName(bleName.toString())
        configPrefs.setBleAddress(bleAddress.toString())
        configPrefs.setWifiAddress(address)
        configPrefs.setWifiPort(port)

        logD(TAG, "P2P connect address:$address port:$port")
        p2pManager.start(address, port)
    }

    /**
     * 重置P2P连接状态
     */
    private fun resetP2pState() {
        logD(TAG, "Resetting P2P state")
        p2pManager.resetConnectionState()
    }

    /**
     * 获取P2P连接信息
     */
    private fun getP2pConnectionInfo(): String {
        return p2pManager.getConnectionInfo()
    }

    /**
     * 获取P2P IP地址
     */
    private fun getP2pIpAddress(): String {
        val connectivityManager =
            application?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        // 遍历所有网络
        for (network in connectivityManager.allNetworks) {
            // 获取网络能力
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_WIFI_P2P)
            ) {
                val linkProperties: LinkProperties? = connectivityManager.getLinkProperties(network)
                linkProperties?.linkAddresses?.forEach { linkAddress ->
                    val address = linkAddress.address
                    if (address is Inet4Address) {
                        return address.hostAddress ?: ""
                    }
                }
            }
        }
        return ""
    }

    /**
     * 关闭连接（用于P2P模式）
     */
    private fun shutdown() {
        logD(TAG, "Shutting down connection")
        displayNaviManager.shutdown()
        p2pManager.stop()
        isP2pConnected = false
        isAutolinkServiceStarted = false
        countDown?.cancel()
        countDown = null
    }

    // =================================================================================
    // Wi-Fi AP Client Management
    // =================================================================================
    private var candidateApSsid: String? = null
    private var candidateApPassword: String? = null
    private var tempApSsid: String? = null
    private var tempApPassword: String? = null

    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            logD(TAG, "WiFi connecting to: $ssid")
        }

        override fun onWifiConnected(ssid: String, ipAddress: String) {
            logD(TAG, "WiFi connected: $ssid, IP: $ipAddress")
            updateWifiStatus(WifiStatus.DeviceConnected)
            displayNaviManager.startTcpServerConnection(ipAddress)
            logD(TAG, "Sending phone IP to instrument: $ipAddress:$LISTEN_PORT")
            sendPhoneIpThroughBle(ipAddress)
        }

        override fun onWifiDisconnected() {
            logD(TAG, "WiFi disconnected")
            updateWifiStatus(WifiStatus.DeviceDisconnected)
            displayNaviManager.shutdown()
        }

        override fun onWifiConnectionFailed(reason: Int) {
            logE(TAG, "WiFi connection failed: $reason")
            ConnectionAnalytics.finishConnection(false, "WiFi connection failed: reason $reason")

            updateWifiStatus(WifiStatus.DeviceDisconnected)

            // 触发连接恢复机制
            val errorType = mapWifiErrorToRecoveryType(reason)
            application?.let {
                connectionRecoveryManager.handleConnectionError(
                    errorType,
                    it,
                    this@ConnectionManager
                )
            }
        }

        override fun onNetworkScanComplete(networks: List<ScanResult>) {
            logD(TAG, "Network scan completed, found ${networks.size} networks")
        }

        override fun requestWifiInfo() {
            <EMAIL>(false)
        }

        override fun onWifiState(opened: Boolean) {
            logD(TAG, "WiFi state changed: $opened")
            if (!opened && connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
                riderServicesExceptionNotiCallbacks.forEach {
                    it.get()?.onSPRiderServicesExceptionNoti(WifiException.WifiDisabled())
                }
            }
        }
    }

    /**
     * 通过BLE发送手机IP地址给仪表端
     */
    private fun sendPhoneIpThroughBle(ipAddress: String) {
        try {
            // 发送AutoLinkConnect消息
            val connect = AutoLinkConnect(ipAddress)
            MessageManager.sendAutoLinkConnect(connect)
            logD(TAG, "Phone IP sent via BLE: $ipAddress:$LISTEN_PORT")
        } catch (e: Exception) {
            logE(TAG, "Failed to send IP address via BLE", e)
        }
    }

    /**
     * 连接到仪表端WiFi热点（AP客户端模式）
     * @param context 应用上下文
     * @param ssid 仪表端WiFi热点名称
     * @param password 热点密码
     */
    private fun connectToInstrumentWifi(context: Context, ssid: String, password: String) {
        logD(TAG, "Start connecting to instrument Wi-Fi: $ssid (AP Mode)")

        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            logI(TAG, "WiFi already connected")
            return
        }
        this.candidateApSsid = ssid
        this.candidateApPassword = password

        // WiFi管理器会自动使用当前活跃的连接会话
        wifiClientManager.connectToWifi(ssid, password)
    }

    private fun setPotentialApCredentials(ssid: String, password: String) {
        this.tempApSsid = ssid
        this.tempApPassword = password
    }


    // =================================================================================
    // Generic Wi-Fi Logic
    // =================================================================================
    /**
     * 请求平台 Wifi 信息
     * @param isReset 是否重置平台 wifi
     */
    internal fun requestWifiInfo(isReset: Boolean = true) {
        logD(TAG, "requestWifiInfo ${mutableConnectionStatus.value.btStatus}")
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            logD(TAG, "WiFi info request")
            val wifiMode = when (wifiConnectionMode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> RiderProtocol.WifiMode.WIFI_AP
                WifiConnectionMode.WIFI_P2P -> RiderProtocol.WifiMode.WIFI_P2P
            }

            logD(TAG, "Requesting WiFi info for mode: $wifiConnectionMode")
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
                RiderProtocol.WifiInfoRequest
                    .newBuilder()
                    .setWifiMode(wifiMode)
                    .setIsResetWifi(isReset).build()
            )
        }
    }

    /**
     * 断开WiFi连接（根据当前连接模式）
     */
    private fun disconnectWifiConnection() {
        logD(TAG, "Disconnecting WiFi connection")
        when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                //wifiClientManager.disconnect()
                logD(TAG, "WiFi AP client disconnected")
            }

            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.stop()
                isP2pConnected = false
                logD(TAG, "WiFi P2P connection disconnected")
            }
        }
    }

    private fun disconnectWifi() {
        disconnectWifiConnection()
    }

    /**
     * 手动重置WiFi连接状态
     */
    internal fun resetWifiConnectionState() {
        logD(TAG, "Resetting WiFi connection state")
        when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.resetConnectionState()
            }

            WifiConnectionMode.WIFI_P2P -> {
                resetP2pState()
            }
        }
        updateWifiStatus(WifiStatus.IDLE)
    }

    /**
     * 获取当前WiFi连接状态信息
     */
    fun getWifiConnectionInfo(): String {
        return when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                val state = wifiClientManager.getCurrentState()
                val attempts = wifiClientManager.getReconnectionAttempts()
                "AP Mode - State: $state, Reconnection attempts: $attempts"
            }

            WifiConnectionMode.WIFI_P2P -> {
                getP2pConnectionInfo()
            }
        }
    }

    /**
     * 检查WiFi连接权限
     */
    internal fun checkWifiPermissions(): Boolean {
        return when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.checkPermissions()
            }

            WifiConnectionMode.WIFI_P2P -> {
                // P2P模式的权限检查逻辑
                true // 暂时返回true，实际应该检查P2P权限
            }
        }
    }

    /**
     * 检查WiFi是否启用
     */
    internal fun isWifiEnabled(): Boolean {
        return when (wifiConnectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.isWifiEnabled()
            }

            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.isWifiEnabled()
            }
        }
    }

    /**
     * 更新WiFi连接状态
     */
    private fun updateWifiStatus(status: WifiStatus) {
        val currentStatus = mutableConnectionStatus.value
        val newStatus = currentStatus.copy(wifiStatus = status)
        mutableConnectionStatus.value = newStatus
        logD(TAG, "WiFi status updated to: $status")
    }


    // =================================================================================
    // Display, Navigation & Transport (DisplayNaviManager)
    // =================================================================================
    private val displayNaviCallback = object : DisplayNaviCallback {
        override fun onDeviceConnected() {
            logD(TAG, "Device connected (TCP/Transport layer established)")
            ConnectionAnalytics.endSocketConnect()
            ConnectionAnalytics.finishConnection(true)
            application?.let { application ->
                val configPrefs = ConfigPreferences.getInstance(application)

                if (wifiConnectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                    if (candidateApSsid != null && candidateApPassword != null) {
                        logD(
                            TAG,
                            "AP Mode fully connected. Saving credentials: SSID=$candidateApSsid, BLE Name=$bleName"
                        )
                        logD(TAG, "TCP/Transport layer established")
                        configPrefs.setApSsid(candidateApSsid!!)
                        configPrefs.setApPassword(candidateApPassword!!)
                        bleName?.let { configPrefs.setBleName(it) }
                        bleAddress?.let { configPrefs.setBleAddress(it) }
                    } else {
                        logW(TAG, "AP Mode connected, but no candidate AP credentials to save.")
                    }
                } else {
                    logD(TAG, "P2P Mode fully connected. Saving BLE credentials if available.")
                    bleName?.let { configPrefs.setBleName(it) }
                    bleAddress?.let { configPrefs.setBleAddress(it) }
                }
            }
            candidateApSsid = null
            candidateApPassword = null

            logD(TAG, "wifi socket connected")
            displayNaviManager.start()
            mutableConnectionStatus.setState {
                copy(
                    wifiStatus = WifiStatus.DeviceConnected,
                    socketStatus = SocketStatus.Connected
                )
            }
        }

        override fun onDeviceDisconnected() {
            logD(TAG, "wifi socket disconnected")
            mutableConnectionStatus.setState {
                copy(
                    wifiStatus = WifiStatus.DeviceDisconnected,
                    socketStatus = SocketStatus.Disconnected
                )
            }
            requestWifiInfo(false)
        }

        override fun onDisplayInitialized(display: Display) {
            presentationCallbacks.forEach {
                it.get()?.onPresentationDisplayReady(display)
            }
        }

        override fun onDisplayReleased(display: Display) {
            presentationCallbacks.forEach {
                it.get()?.onPresentationDisplayReleased(display)
            }
        }

        override fun onVideoChannelReady() {
            //todo 跟 navimode 强相关
//            serviceCallbacks.forEach {
//                it.get()?.onVideoChannelReady()
//            }
        }

        override fun onRequestMediaProjection() {
            presentationCallbacks.forEach {
                it.get()?.onMediaProjectionPermissionRequired()
            }
        }

        override fun onMirrorStart() {
            //todo 维护mirror 的状态
//            serviceCallbacks.forEach {
//                it.get()?.onMirrorStart()
//            }
        }

        override fun onMirrorStop() {
            //todo 维护mirror 的状态
//            serviceCallbacks.forEach {
//                it.get()?.onMirrorStop()
//            }
        }

        override fun onTcpConnectionFailed(reason: Int) {
            logE(TAG, "TCP connection failed: $reason")

            // 记录TCP连接失败，结束整个连接会话
            ConnectionAnalytics.finishConnection(false, "TCP connection failed: reason $reason")

            mutableConnectionStatus.setState { copy(socketStatus = SocketStatus.Disconnected) }
            // 触发TCP连接恢复机制
            val errorType = mapTcpErrorToRecoveryType(reason)
            application?.let {
                connectionRecoveryManager.handleConnectionError(
                    errorType,
                    it,
                    this@ConnectionManager
                )
            }
        }
    }

    private fun setNaviMode(naviMode: SPNaviMode) {
        displayNaviManager.setNaviMode(naviMode)
    }

    internal fun setMediaProjection(mediaProjection: MediaProjection) {
        displayNaviManager.setMediaProjection(mediaProjection)
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    private fun startScreenProjection(): Boolean {
        return displayNaviManager.startScreenProjection()
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    private fun stopScreenProjection(): Boolean {
        return displayNaviManager.stopScreenProjection()
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    private fun sendOrientation(isLandscape: Int, rotation: Int = Platform.NORMAL) {
        displayNaviManager.sendOrientation(isLandscape, rotation)
    }

    /**
     * 启动连接建立过程
     */
    private fun startConnectionEstablishing() {
        // TCP连接开始记录已在displayNaviManager的回调中处理
        displayNaviManager.startConnectionEstablishing()
    }

    /**
     * 处理P2P连接已存在的通用逻辑
     * @param logMessage 日志信息
     */
    private fun handleP2pConnectionExist(logMessage: String) {
        logD(TAG, logMessage)
        ConnectionAnalytics.endWifiScan()
        ConnectionAnalytics.startWifiConnect()
        ConnectionAnalytics.endWifiConnect()

        isP2pConnected = true
        startConnectionEstablishing()
        sendPhoneIpThroughBle(getP2pIpAddress())
        waitForAutoLinkConnect(2)
    }

    private fun requestLockScreenDisplay() {
        displayNaviManager.requestLockScreenDisplay()
    }

    // =================================================================================
    // Authentication & Protocol Handshake
    // =================================================================================
    private fun requestProtocolVersion() {
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_REQUEST_VALUE,
                RiderProtocol.ProtocolVerRequest.newBuilder().build()
            )
            connectWithTimeout()
        }
    }

    private fun connectWithTimeout() {
        if (connectionTimer == null) {
            connectionTimer = Timer()
        }
        connectionTimer?.schedule(object : TimerTask() {
            override fun run() {
                // 记录连接超时，结束整个连接会话
                ConnectionAnalytics.finishConnection(false, "Connection timeout after 3 seconds")

                riderServicesExceptionNotiCallbacks.forEach {
                    it.get()?.onSPRiderServicesExceptionNoti(NetworkException.ConnectionTimeout())
                }
                disconnect()
            }
        }, 3000)
    }

    private fun cancelConnectWithTimeout() {
        connectionTimer?.cancel()
        connectionTimer?.purge()
        connectionTimer = null
    }

    /**
     * 匹配版本
     * @param version 版本
     */
    private fun checkVersion(
        version: RiderProtocol.ProtocolVerNotification,
        isManualConnected: Boolean
    ) {
        cancelConnectWithTimeout()
        if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
            if (RiderBleManager.isConfigConnect() && !isManualConnected) {

                if (wifiConnectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                    requestWifiInfo(false)
                } else { // WIFI_P2P mode (existing logic)
                    application?.let {
                        val configPreferences = ConfigPreferences.getInstance(it.applicationContext)
                        val address = configPreferences.getWifiAddress()
                        val port = configPreferences.getWifiPort()
                        if (address != null && port != 0) {
                            if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
                                logD(TAG, "P2P Mode: WiFi already connected (auto-connect)")
                                return
                            }
                            logD(
                                TAG,
                                "P2P Mode: Attempting auto-connect with saved P2P info, Address: $address"
                            )
                            // P2P模式使用ConnectionManager的方法
                            startSearchWifiAndConnect(
                                it.applicationContext,
                                address,
                                port
                            )

                        } else {
                            logD(
                                TAG,
                                "P2P Mode: No saved P2P info for auto-connect, requesting info."
                            )
                            requestWifiInfo(false) // Request info if no saved P2P credentials
                        }
                    }
                }
            } else { // Auto-connect disabled or manual click
                logD(TAG, "Auto-connect disabled or manual BLE item click, requesting WiFi info.")
                requestWifiInfo(false)
            }
        } else {
            // 记录协议版本不匹配，结束整个连接会话
            ConnectionAnalytics.finishConnection(
                false,
                "Protocol version mismatch: expected ${RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE}.${RiderProtocol.ProtocolVersion.MINOR_VERSION_VALUE}, got ${version.major}.${version.minor}"
            )

            riderServicesExceptionNotiCallbacks.forEach {
                it.get()?.onSPRiderServicesExceptionNoti(
                    ProtocolException.VersionMismatch(
                        "${RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE}.${RiderProtocol.ProtocolVersion.MINOR_VERSION_VALUE}",
                        "${version.major}.${version.minor}"
                    )
                )
            }
            disconnect()
        }
    }

    /**
     * 请求服务器激活
     * @param sdkKey 平台 SDK 的 key
     * @param macAddr 平台的MAC地址
     * @param timestamp 时间戳
     * @param sign 平台签名
     */
    private fun requestActivate(
        sdkKey: String, macAddr: String, timestamp: String, sign: String
    ) {
        mainScope.launch(Dispatchers.IO) {
            val activeNotification = RiderProtocol.ActivateNotification.newBuilder()
            try {
                val result = authorizeRepository.requestActivateStatus(
                    key = sdkKey, macAddr, timestamp, sign
                )
                activeNotification.result = result.getOrThrow().status
                activeNotification.uuid = result.getOrThrow().uuid
            } catch (e: Exception) {
                activeNotification.result = NETWORK_ERROR
            }
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_ACTIVE_NOTIFICATION_VALUE,
                activeNotification.build()
            )
            if (activeNotification.result != SUCCESS) {
                riderServicesExceptionNotiCallbacks.forEach {
                    it.get()
                        ?.onSPRiderServicesExceptionNoti(AuthException.ActivationFailed("服务器激活失败"))
                }
                disconnect()
            }
        }
    }

    /**
     * 请求服务器验证
     * @param productKey 产品 Key
     * @param address 平台的MAC地址
     * @param uuid 产品唯一ID
     * @param timestamp 时间戳
     * @param licenseSign 证书签名
     * @param sign 平台签名
     */
    private fun requestVerify(
        productKey: String,
        address: String,
        uuid: String,
        timestamp: String,
        licenseSign: String,
        sign: String
    ) {
        val complianceNotification = RiderProtocol.ComplianceNotification.newBuilder()
        mainScope.launch {
            flow {
                emit(
                    authorizeRepository.requestCheckStatus(
                        productKey, address, uuid, timestamp, licenseSign, sign
                    )
                )
            }
                .retry(3) {
                    if (it is Exception) {
                        delay(1000)
                        true
                    } else {
                        false
                    }
                }
                .catch {
                    complianceNotification.result = NETWORK_ERROR
                    logE(TAG, "requestVerify: $it")
                }.flowOn(Dispatchers.IO)
                .collect {
                    complianceNotification.result = it.getOrThrow().status
                }
            MessageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_NOTIFICATION_VALUE,
                complianceNotification.build()
            )
            if (complianceNotification.result != SUCCESS) {
                if (complianceNotification.result == NETWORK_ERROR) {
                    riderServicesExceptionNotiCallbacks.forEach {
                        it.get()
                            ?.onSPRiderServicesExceptionNoti(NetworkException.NetworkError())
                    }
                    disconnect(false)
                } else {
                    riderServicesExceptionNotiCallbacks.forEach {
                        it.get()
                            ?.onSPRiderServicesExceptionNoti(AuthException.InvalidCredentials())
                    }
                    disconnect()
                }
            } else {
                requestProtocolVersion()
            }
        }
    }

    // =================================================================================
    // Section 11: AutoLink Connection Logic
    // =================================================================================

    /**
     * 等待AutoLink连接
     */
    private fun waitForAutoLinkConnect(retryCount: Int) {
        countDown = countDownByFlow(
            10, 1000, mainScope,
            onTick = {
                logD(TAG, "次数：$retryCount,倒计时：$it,Autolink启动情况:$isAutolinkServiceStarted")
                if (isAutolinkServiceStarted || !isP2pConnected) {
                    countDown?.cancel()
                }
            }, onFinish = {
                if (!isAutolinkServiceStarted) {
                    if (retryCount == 6) {
                        logD(TAG, "AutoLink 启动失败，屏蔽投屏导航")
                    } else {
                        sendPhoneIpThroughBle(getP2pIpAddress())
                        waitForAutoLinkConnect(retryCount + 1)
                    }
                }
            })
    }

    /**
     * Autolink启动成功
     */
    private fun startAutolinkServiceSuccess() {
        isAutolinkServiceStarted = true
    }

    // =================================================================================
    // Connection Recovery
    // =================================================================================
    private val recoveryListener = object : ConnectionRecoveryManager.ErrorHandlerListener {
        override fun onRecoveryStarted(errorType: ConnectionRecoveryManager.ErrorType) {
            logI(TAG, "Connection recovery started for error: $errorType")
        }

        override fun onRecoveryProgress(attempt: Int, maxAttempts: Int) {
            logI(TAG, "Recovery progress: $attempt/$maxAttempts")
        }

        override fun onRecoverySuccess(strategy: ConnectionRecoveryManager.RecoveryStrategy) {
            logI(TAG, "Connection recovery succeeded with strategy: $strategy")
        }

        override fun onRecoveryFailed(
            errorType: ConnectionRecoveryManager.ErrorType,
            finalStrategy: ConnectionRecoveryManager.RecoveryStrategy
        ) {
            logE(
                TAG,
                "Connection recovery failed for error: $errorType, final strategy: $finalStrategy"
            )
        }

        override fun onUserGuidanceRequired(
            errorType: ConnectionRecoveryManager.ErrorType,
            guidance: String
        ) {
            logW(TAG, "User guidance required for error: $errorType, guidance: $guidance")
            //todo wifi exception
        }
    }

    /**
     * 将WiFi错误代码映射到恢复错误类型
     */
    private fun mapWifiErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -1 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            -2 -> ConnectionRecoveryManager.ErrorType.AUTHENTICATION_FAILED
            -3 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -4 -> ConnectionRecoveryManager.ErrorType.WIFI_DISABLED
            -5 -> ConnectionRecoveryManager.ErrorType.PERMISSION_ERROR
            -6 -> ConnectionRecoveryManager.ErrorType.IP_ACQUISITION_FAILED
            else -> ConnectionRecoveryManager.ErrorType.UNKNOWN_ERROR
        }
    }

    /**
     * 将TCP错误代码映射到恢复错误类型
     */
    private fun mapTcpErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -7 -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
            -8 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -9 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            else -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
        }
    }

    /**
     * 手动触发连接恢复
     */
    fun triggerConnectionRecovery(errorType: ConnectionRecoveryManager.ErrorType) {
        application?.let {
            connectionRecoveryManager.handleConnectionError(errorType, it.applicationContext, this)
        }
    }

    /**
     * 停止连接恢复
     */
    fun stopConnectionRecovery() {
        connectionRecoveryManager.stopRecovery()
    }

    /**
     * 获取连接恢复状态
     */
    fun isConnectionRecovering(): Boolean {
        return connectionRecoveryManager.isRecovering()
    }

    /**
     * 获取当前恢复尝试次数
     */
    fun getCurrentRecoveryAttempts(): Int {
        return connectionRecoveryManager.getCurrentRecoveryAttempts()
    }

    // =================================================================================
    // Service Callback Forwarding
    // =================================================================================

    /**
     * 导航模式发改变
     * @param mode 模式
     * @see SPNaviMode
     */
    internal fun sendNaviModeChange(mode: SPNaviMode) {
        //todo 功能暂时不支持
    }

    private fun sendNaviModeChangeResponse(naviMode: RiderProtocol.NaviMode, ready: Boolean) {
        changeNaviModeCallbacks.forEach {
            it.get()?.onNaviModeChange(getNaviMode(naviMode), ready)
        }
    }

    private fun requestWeatherInfo() {
        //todo(请求天气信息)
    }

    private fun sendNaviModeStartResponse(naviMode: RiderProtocol.NaviMode) {
        //TODO(内部处理)
//        serviceCallbacks.forEach { callback ->
//            callback.get()?.onNaviModeStartResponse(getNaviMode(naviMode))
//        }
    }

    private fun sendNaviModeStopResponse(naviMode: RiderProtocol.NaviMode) {
        //TODO(内部处理)
//        serviceCallbacks.forEach { callback ->
//            callback.get()?.onNaviModeStopResponse(getNaviMode(naviMode))
//        }
    }

    private fun getNaviMode(naviMode: RiderProtocol.NaviMode): SPNaviMode {
        return when (naviMode) {
            RiderProtocol.NaviMode.DEFAULT_NAVI -> SPNaviMode.SPNaviModeDefaultNavi
            RiderProtocol.NaviMode.SIMPLE_NAVI -> SPNaviMode.SPNaviModeSimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> SPNaviMode.SPNaviModeScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> SPNaviMode.SPNaviModeMirrorNavi
            RiderProtocol.NaviMode.CRUISE_NAVI -> SPNaviMode.SPNaviModeCruiseNavi
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> SPNaviMode.SPNaviModeLockScreenNavi
            RiderProtocol.NaviMode.NO_NAVI -> SPNaviMode.SPNaviModeNoNavi
        }
    }

    internal fun getProtocolVersion(): String {
        return protocolVersion
    }


    // =================================================================================
    // Analytics
    // =================================================================================
    /**
     * 获取连接信息
     * @return 连接信息
     * @see Connection
     */
    internal fun getConnectStatus(): Connection {
        return mutableConnectionStatus.value
    }

    internal fun getCurrentConnectDevice(): BluetoothDevice? {
        return RiderBleManager.getCurrentConnectDevice()
    }

    /**
     * 获取连接分析统计信息
     */
    fun getConnectionAnalyticsInfo(): String {
        val activeConnections = ConnectionAnalytics.getActiveConnectionCount()

        return buildString {
            appendLine("=== Connection Analytics ===")
            appendLine("Active connections: $activeConnections")
        }
    }

    /**
     * 初始化连接时间记录器（仅Debug模式）
     */
    private fun initConnectionTimeTracker(context: Context) {
        SimpleConnectionLogger.init(context)
    }

    /**
     * 清除连接时间记录（仅Debug模式）
     */
    fun clearConnectionTimeRecords() {
        SimpleConnectionLogger.clearLogs()
    }

    // =================================================================================
    // Lifecycle & Initialization
    // =================================================================================

    /**
     * 初始化
     * @param application
     */
    internal fun init(application: Application) {
        this.application = application
        MessageManager.registerCallback(messageCallback)
        setupScreenStateListeners(application)
        setupAppStateListener(application)
        initConnectionTimeTracker(application.applicationContext)
        RiderBleManager.addCallback(bleCallback)
        RiderBleManager.registerBroadcastReceivers(application.applicationContext)
        // 设置连接恢复监听器
        connectionRecoveryManager.setErrorHandlerListener(recoveryListener)
    }

    /**
     * 销毁并释放资源
     * @param context
     */
    internal fun destroy(context: Context) {
        RiderBleManager.stopScan()
        RiderBleManager.removeCallback(bleCallback)
        RiderBleManager.unregisterBroadcastReceivers(context)

        // 停止连接恢复
        connectionRecoveryManager.stopRecovery()
    }

    internal fun release() {
        disconnect()
        mutableConnectionStatus.setState {
            copy(
                wifiStatus = WifiStatus.IDLE,
                btStatus = BleStatus.IDLE,
                socketStatus = SocketStatus.IDLE
            )
        }
        displayNaviManager.removeCallback(displayNaviCallback)
        RiderBleManager.release()
    }

    internal fun getSDKVersion(): String {
        return sdkVersion
    }

    internal fun getDeviceConfig(): DeviceConfig? {
        return deviceConfig
    }

    internal fun setWifiConnectionMode(mode: WifiConnectionMode) {
        wifiConnectionMode = mode
        logD(TAG, "WiFi connection mode set to: $mode")
    }

    internal fun getWifiConnectionMode(): WifiConnectionMode {
        return wifiConnectionMode
    }


    private fun setupScreenStateListeners(application: Application) {
        ScreenBrightnessUtils.addScreenListener(object :
            ScreenBrightnessUtils.OnScreenStateUpdateListener {
            override fun whenScreenOff() {
                logD(TAG, "Screen off, current mode: $currentNaviMode")
                // 屏幕关闭时，如果现在是mirror mode就切换到锁屏导航，其他模式不切
                if (currentNaviMode == SPNaviMode.SPNaviModeMirrorNavi) {
                    changeNaviMode(SPNaviMode.SPNaviModeLockScreenNavi)
                }
            }

            override fun whenScreenOn() {
                logD(TAG, "Screen on, current mode: $currentNaviMode")
            }

            override fun whenUserPresent() {
                logD(TAG, "User present, current mode: $currentNaviMode")
                // 用户解锁时，如果是锁屏模式才切换到巡航模式，其他模式不切
                if (currentNaviMode == SPNaviMode.SPNaviModeLockScreenNavi) {
                    changeNaviMode(SPNaviMode.SPNaviModeCruiseNavi)
                }
            }

        })
        ScreenBrightnessUtils.registerScreenBroadcastReceiver(application)
    }


    private fun setupAppStateListener(application: Application) {
        AppBackgroundManager.init(application)
        AppBackgroundManager.addListener(object : AppBackgroundManager.AppStateListener {
            override fun onAppStateChanged(newState: AppBackgroundManager.AppState) {
                logD(TAG, "App state changed: $newState, current mode: $currentNaviMode")

                when (newState) {
                    AppBackgroundManager.AppState.FOREGROUND -> {
                        // 应用回到前台时，如果是锁屏模式才切换到巡航模式，其他模式不切
                        if (currentNaviMode == SPNaviMode.SPNaviModeLockScreenNavi) {
                            changeNaviMode(SPNaviMode.SPNaviModeCruiseNavi)
                        }
                    }

                    AppBackgroundManager.AppState.BACKGROUND -> {
                        // 应用进入后台时不做模式切换
                        logD(TAG, "App entered background, keeping current mode: $currentNaviMode")
                    }
                }
            }
        })
    }

    @Synchronized
    internal fun addBleListCallback(callback: BleListCallback) {
        bleListCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeBleListCallback(callback: BleListCallback) {
        bleListCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addBleConnectStateCallback(callback: BleStateCallback) {
        bleStateCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeBleConnectStateCallback(callback: BleStateCallback) {
        bleStateCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addWifiInfoCallback(callback: WifiInfoCallback) {
        wifiInfoCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeWifiInfoCallback(callback: WifiInfoCallback) {
        wifiInfoCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        changeNaviModeCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeChangeNaviModeCallback(callback: ChangeNaviModeCallback) {
        changeNaviModeCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addConnectionStatusCallback(callback: ConnectionStatusCallback) {
        connectionStatusCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeConnectionStatusCallback(callback: ConnectionStatusCallback) {
        changeNaviModeCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addPermissionDetectionCallback(callback: PermissionDetectionCallback) {
        permissionDetectionCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removePermissionDetectionCallback(callback: PermissionDetectionCallback) {
        permissionDetectionCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        riderServicesExceptionNotiCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeRiderServicesExceptionNotiCallback(callback: SPRiderServicesExceptionNotiCallback) {
        riderServicesExceptionNotiCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        receiveCommonDataCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeReceiveCommonDataCallback(callback: ReceiveCommonDataCallback) {
        receiveCommonDataCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addPresentationCallback(callback: PresentationCallback) {
        presentationCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removePresentationCallback(callback: PresentationCallback) {
        presentationCallbacks.removeIf { it.get() == callback }
    }

    @Synchronized
    internal fun addNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        naviDayOrNightChangeCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    internal fun removeNaviDayOrNightChangeCallback(callback: NaviDayOrNightChangeCallback) {
        naviDayOrNightChangeCallbacks.removeIf { it.get() == callback }
    }

    internal fun connectWifi() {
        TODO("连接wifi")
    }

    internal fun getWifiInfo(): SPWifiInfo {
        return spWifiInfo
    }

    // Navigation state query methods
    internal fun getCurrentNaviMode(): SPNaviMode = currentNaviMode
    internal fun getPreviousNaviMode(): SPNaviMode = previousNaviMode

    // Convenience methods for checking specific modes
    internal fun isMirrorMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeMirrorNavi
    internal fun isCruiseMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeCruiseNavi
    internal fun isScreenNaviMode(): Boolean = currentNaviMode == SPNaviMode.SPNaviModeScreenNavi

    internal fun startNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "startNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            // 保存上一个模式
            previousNaviMode = currentNaviMode
            currentNaviMode = naviMode

            MessageManager.startNaviMode(naviMode)

            // 通知状态变化
            notifyNaviModeChanged(previousNaviMode, currentNaviMode)
        }
    }

    internal fun changeNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "changeNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            previousNaviMode = currentNaviMode
            currentNaviMode = naviMode
            // todo
            //MessageManager.changeNaviMode(naviMode)
            notifyNaviModeChanged(previousNaviMode, currentNaviMode)
        }
    }

    internal fun stopNaviMode(naviMode: SPNaviMode) {
        logD(TAG, "stopNaviMode: $currentNaviMode -> $naviMode")

        if (isBleConnected()) {
            previousNaviMode = currentNaviMode

            // 确定停止后的模式
            currentNaviMode = when {
                // 如果停止的是当前模式，则根据配置选择默认模式
                naviMode == currentNaviMode -> {
                    if (getDeviceConfig()?.isSupportCruise == true) {
                        SPNaviMode.SPNaviModeCruiseNavi
                    } else {
                        SPNaviMode.SPNaviModeNoNavi
                    }
                }
                // 否则保持当前模式不变
                else -> currentNaviMode
            }

            MessageManager.stopNaviMode(naviMode)
            notifyNaviModeChanged(previousNaviMode, currentNaviMode)
        }
    }

    private fun notifyNaviModeChanged(oldMode: SPNaviMode, newMode: SPNaviMode) {
        mainScope.launch {
            changeNaviModeCallbacks.forEach { callbackRef ->
                callbackRef.get()?.onNaviModeChanged(oldMode, newMode)
            }
        }
    }


    fun connectUseQRCodeValue(value: String) {
        logD(TAG, "Starting connection using QR code value")
        val macAddress = QRCodeUtils.parseQRCodeForMacAddress(value)
        if (macAddress == null) {
            logE(TAG, "Failed to parse MAC address from QR code, aborting connection")
            return
        }

        application?.let { app ->
            val success = RiderBleManager.connectByMacAddress(macAddress, app)
            if (success) {
                logD(TAG, "Successfully initiated BLE connection to: $macAddress")
            } else {
                logE(TAG, "Failed to initiate BLE connection to: $macAddress")
            }
        } ?: run {
            logE(TAG, "Application context is null, cannot connect")
        }
    }

    fun sendCommonData(byteArray: ByteArray) {}


    init {
        connectionStatus.collectWithScope(mainScope) { connection ->
            connectionStatusCallbacks.forEach {
                it.get()?.onConnectionStatusChanged(connection)
            }
        }
        displayNaviManager.addCallback(displayNaviCallback)
    }
}