package com.link.riderservice.feature.display.protocol.source


import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.feature.display.config.BluetoothConfig
import com.link.riderservice.feature.display.protocol.project.BluetoothCallbacks
import com.link.riderservice.feature.display.protocol.project.BluetoothEndpoint
import com.link.riderservice.feature.display.protocol.project.Protos
import com.link.riderservice.feature.display.protocol.project.UserDefined
import com.link.riderservice.feature.display.protocol.project.UserDefinedCallbacks
import java.util.TimerTask
import java.util.concurrent.ScheduledExecutorService


internal class ALUserDefined : ALServiceBase {


    interface UserDefinedListener {
        fun onRawDataRequest(msg: ByteArray)

        fun onRawDataResponse()
    }

    private var userDefined: UserDefined? = null
    fun sendRawDataRequest(msg: ByteArray) {
        userDefined?.nativeRawDataRequest(msg)
    }

    fun sendRawDataResponse() {
        userDefined?.nativeRawDataResponse()
    }

    override fun destroy() {
        userDefined?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return userDefined?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_BLUETOOTH
    override val nativeInstance: Long
        get() = userDefined?.nativeInstance ?: 0

    companion object {
        private const val TAG = "ALBluetoothEndpoint"
    }

    init {
        val userDefinedCallbacks: UserDefinedCallbacks = object : UserDefinedCallbacks {
            override fun onChannelOpened(): Int {
                TODO("Not yet implemented")
            }

            override fun onRawDataRequest(msg: ByteArray) {
                TODO("Not yet implemented")
            }

            override fun onRawDataResponse() {
                TODO("Not yet implemented")
            }

        }
        userDefined = UserDefined(userDefinedCallbacks)
    }
}