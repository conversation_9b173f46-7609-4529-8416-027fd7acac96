package com.link.riderservice.core.utils.ble

import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE

/**
 * 二维码解析工具类
 * 
 * 提供二维码内容解析相关的工具方法
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-03
 */
object QRCodeUtils {
    private const val TAG = "QRCodeUtils"
    
    /**
     * 二维码信息数据类
     * @param btMacAddress 蓝牙MAC地址
     * @param wifiName WiFi名称
     * @param wifiPassword WiFi密码
     * @param wifiSecurity WiFi安全类型
     */
    data class QRCodeInfo(
        val btMacAddress: String,
        val wifiName: String? = null,
        val wifiPassword: String? = null,
        val wifiSecurity: String? = null
    )
    
    /**
     * 解析二维码内容并提取蓝牙MAC地址
     * @param qrCodeValue 二维码字符串内容
     * @return 格式化后的MAC地址，解析失败返回null
     */
    fun parseQRCodeForMacAddress(qrCodeValue: String): String? {
        try {
            logD(TAG, "Parsing QR code value for MAC address: $qrCodeValue")
            
            // 解析格式：BtMacAddress=597B004A0F53;WifiName=Geminic340dc;WifiPassword=88888888;WifiSecurity=wpa2-psk
            val pairs = qrCodeValue.split(";")
            
            for (pair in pairs) {
                val keyValue = pair.split("=", limit = 2)
                if (keyValue.size == 2 && keyValue[0].trim() == "BtMacAddress") {
                    val macAddress = keyValue[1].trim()
                    // 格式化MAC地址（添加冒号分隔符）
                    return formatMacAddress(macAddress)
                }
            }
            
            logE(TAG, "BtMacAddress not found in QR code")
            return null
            
        } catch (e: Exception) {
            logE(TAG, "Failed to parse QR code value: $qrCodeValue", e)
            return null
        }
    }
    
    /**
     * 解析二维码内容并提取完整信息
     * @param qrCodeValue 二维码字符串内容
     * @return 解析后的二维码信息，解析失败返回null
     */
    fun parseQRCodeInfo(qrCodeValue: String): QRCodeInfo? {
        try {
            logD(TAG, "Parsing QR code value: $qrCodeValue")
            
            // 解析格式：BtMacAddress=597B004A0F53;WifiName=Geminic340dc;WifiPassword=88888888;WifiSecurity=wpa2-psk
            val pairs = qrCodeValue.split(";")
            val dataMap = mutableMapOf<String, String>()
            
            for (pair in pairs) {
                val keyValue = pair.split("=", limit = 2)
                if (keyValue.size == 2) {
                    dataMap[keyValue[0].trim()] = keyValue[1].trim()
                }
            }
            
            // 提取字段
            val btMacAddress = dataMap["BtMacAddress"]
            val wifiName = dataMap["WifiName"]
            val wifiPassword = dataMap["WifiPassword"]
            val wifiSecurity = dataMap["WifiSecurity"]
            
            // 验证必需字段
            if (btMacAddress.isNullOrEmpty()) {
                logE(TAG, "BtMacAddress is missing or empty in QR code")
                return null
            }
            
            // 格式化蓝牙MAC地址
            val formattedMacAddress = formatMacAddress(btMacAddress)
            if (formattedMacAddress == null) {
                logE(TAG, "Invalid MAC address format: $btMacAddress")
                return null
            }
            
            val qrCodeInfo = QRCodeInfo(
                btMacAddress = formattedMacAddress,
                wifiName = wifiName,
                wifiPassword = wifiPassword,
                wifiSecurity = wifiSecurity
            )
            
            logD(TAG, "Successfully parsed QR code: $qrCodeInfo")
            return qrCodeInfo
            
        } catch (e: Exception) {
            logE(TAG, "Failed to parse QR code value: $qrCodeValue", e)
            return null
        }
    }
    
    /**
     * 格式化MAC地址，添加冒号分隔符
     * @param macAddress 原始MAC地址（如：597B004A0F53）
     * @return 格式化后的MAC地址（如：59:7B:00:4A:0F:53），格式错误返回null
     */
    fun formatMacAddress(macAddress: String): String? {
        // 移除所有非十六进制字符
        val cleanMac = macAddress.replace(Regex("[^0-9A-Fa-f]"), "")
        
        // 验证长度是否为12个字符
        if (cleanMac.length != 12) {
            logE(TAG, "Invalid MAC address length: ${cleanMac.length}, expected 12")
            return null
        }
        
        // 验证是否都是有效的十六进制字符
        if (!cleanMac.matches(Regex("^[0-9A-Fa-f]{12}$"))) {
            logE(TAG, "Invalid MAC address format: contains non-hex characters")
            return null
        }
        
        // 添加冒号分隔符
        return cleanMac.chunked(2).joinToString(":")
    }
    
    /**
     * 验证MAC地址格式是否正确
     * @param macAddress MAC地址字符串
     * @return 是否为有效的MAC地址格式
     */
    fun isValidMacAddress(macAddress: String): Boolean {
        // MAC地址格式：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX
        val macPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        return macAddress.matches(macPattern.toRegex())
    }
}
